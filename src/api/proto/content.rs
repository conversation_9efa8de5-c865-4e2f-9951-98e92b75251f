// This file is @generated by prost-build.
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct CollectionModel {
    #[prost(string, tag = "1")]
    pub id: ::prost::alloc::string::String,
    #[prost(string, tag = "2")]
    pub name: ::prost::alloc::string::String,
    #[prost(string, tag = "3")]
    pub identifier: ::prost::alloc::string::String,
    #[prost(message, optional, tag = "4")]
    pub created_at: ::core::option::Option<::prost_types::Timestamp>,
    #[prost(message, optional, tag = "5")]
    pub updated_at: ::core::option::Option<::prost_types::Timestamp>,
    #[prost(string, tag = "6")]
    pub created_by: ::prost::alloc::string::String,
    #[prost(string, tag = "7")]
    pub updated_by: ::prost::alloc::string::String,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct GetCollectionRequest {
    #[prost(string, tag = "1")]
    pub collection_id: ::prost::alloc::string::String,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct GetCollectionResponse {
    #[prost(bool, tag = "1")]
    pub status: bool,
    #[prost(message, optional, tag = "2")]
    pub data: ::core::option::Option<CollectionModel>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct StoreCollectionRequest {
    #[prost(string, tag = "1")]
    pub name: ::prost::alloc::string::String,
    #[prost(string, tag = "2")]
    pub identifier: ::prost::alloc::string::String,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct StoreCollectionResponse {
    #[prost(bool, tag = "1")]
    pub status: bool,
    #[prost(message, optional, tag = "2")]
    pub data: ::core::option::Option<CollectionModel>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UpdateCollectionRequest {
    #[prost(string, tag = "1")]
    pub id: ::prost::alloc::string::String,
    #[prost(string, tag = "2")]
    pub name: ::prost::alloc::string::String,
    #[prost(string, tag = "3")]
    pub identifier: ::prost::alloc::string::String,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UpdateCollectionResponse {
    #[prost(bool, tag = "1")]
    pub status: bool,
    #[prost(message, optional, tag = "2")]
    pub data: ::core::option::Option<CollectionModel>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ContentFieldFieldContent {
    #[prost(string, optional, tag = "1")]
    pub text_value: ::core::option::Option<::prost::alloc::string::String>,
    #[prost(int64, optional, tag = "2")]
    pub int_value: ::core::option::Option<i64>,
    #[prost(string, repeated, tag = "3")]
    pub array_value: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    #[prost(double, optional, tag = "4")]
    pub float_value: ::core::option::Option<f64>,
    #[prost(bool, optional, tag = "5")]
    pub bool_value: ::core::option::Option<bool>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ContentRadioFieldData {
    #[prost(string, tag = "1")]
    pub label: ::prost::alloc::string::String,
    #[prost(string, tag = "2")]
    pub value: ::prost::alloc::string::String,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ContentCheckboxFieldData {
    #[prost(string, tag = "1")]
    pub label: ::prost::alloc::string::String,
    #[prost(string, tag = "2")]
    pub value: ::prost::alloc::string::String,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ContentSelectFieldData {
    #[prost(string, tag = "1")]
    pub label: ::prost::alloc::string::String,
    #[prost(string, tag = "2")]
    pub value: ::prost::alloc::string::String,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ContentFieldData {
    #[prost(message, repeated, tag = "1")]
    pub content_select_field_options: ::prost::alloc::vec::Vec<ContentSelectFieldData>,
    #[prost(message, repeated, tag = "2")]
    pub content_checkbox_field_data: ::prost::alloc::vec::Vec<ContentCheckboxFieldData>,
    #[prost(message, repeated, tag = "3")]
    pub content_radio_field_data: ::prost::alloc::vec::Vec<ContentRadioFieldData>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ContentFieldModel {
    #[prost(string, tag = "1")]
    pub name: ::prost::alloc::string::String,
    #[prost(string, tag = "2")]
    pub identifier: ::prost::alloc::string::String,
    #[prost(string, tag = "3")]
    pub data_type: ::prost::alloc::string::String,
    #[prost(string, tag = "4")]
    pub field_type: ::prost::alloc::string::String,
    #[prost(message, optional, tag = "5")]
    pub field_content: ::core::option::Option<ContentFieldFieldContent>,
    #[prost(message, optional, tag = "6")]
    pub field_data: ::core::option::Option<ContentFieldData>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ContentModel {
    #[prost(string, tag = "1")]
    pub id: ::prost::alloc::string::String,
    #[prost(string, tag = "2")]
    pub name: ::prost::alloc::string::String,
    #[prost(string, tag = "3")]
    pub identifier: ::prost::alloc::string::String,
    #[prost(message, optional, tag = "4")]
    pub created_at: ::core::option::Option<::prost_types::Timestamp>,
    #[prost(message, optional, tag = "5")]
    pub updated_at: ::core::option::Option<::prost_types::Timestamp>,
    #[prost(string, tag = "6")]
    pub created_by: ::prost::alloc::string::String,
    #[prost(string, tag = "7")]
    pub updated_by: ::prost::alloc::string::String,
    #[prost(message, repeated, tag = "8")]
    pub content_fields: ::prost::alloc::vec::Vec<ContentFieldModel>,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct CollectionAllRequest {}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct CollectionAllResponse {
    #[prost(bool, tag = "1")]
    pub status: bool,
    #[prost(message, repeated, tag = "2")]
    pub data: ::prost::alloc::vec::Vec<CollectionModel>,
}
/// Content paginate API
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ContentPaginateRequest {
    #[prost(string, tag = "1")]
    pub content_type: ::prost::alloc::string::String,
    #[prost(int64, optional, tag = "2")]
    pub page: ::core::option::Option<i64>,
    #[prost(string, optional, tag = "3")]
    pub order: ::core::option::Option<::prost::alloc::string::String>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ContentPaginateResponse {
    #[prost(bool, tag = "1")]
    pub status: bool,
    #[prost(message, optional, tag = "2")]
    pub data: ::core::option::Option<content_paginate_response::ContentPaginateData>,
}
/// Nested message and enum types in `ContentPaginateResponse`.
pub mod content_paginate_response {
    #[derive(Clone, Copy, PartialEq, ::prost::Message)]
    pub struct ContentPagination {
        #[prost(int64, tag = "1")]
        pub total: i64,
    }
    #[derive(Clone, PartialEq, ::prost::Message)]
    pub struct ContentPaginateData {
        #[prost(message, optional, tag = "1")]
        pub pagination: ::core::option::Option<ContentPagination>,
        #[prost(message, repeated, tag = "2")]
        pub data: ::prost::alloc::vec::Vec<super::ContentModel>,
    }
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct StoreContentFieldModel {
    #[prost(string, tag = "1")]
    pub name: ::prost::alloc::string::String,
    #[prost(string, tag = "2")]
    pub identifier: ::prost::alloc::string::String,
    #[prost(string, tag = "3")]
    pub data_type: ::prost::alloc::string::String,
    #[prost(string, tag = "4")]
    pub field_type: ::prost::alloc::string::String,
    #[prost(message, optional, tag = "5")]
    pub field_content: ::core::option::Option<ContentFieldFieldContent>,
    #[prost(message, optional, tag = "6")]
    pub field_data: ::core::option::Option<ContentFieldData>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct StoreContentRequest {
    #[prost(string, tag = "1")]
    pub name: ::prost::alloc::string::String,
    #[prost(string, tag = "2")]
    pub identifier: ::prost::alloc::string::String,
    #[prost(string, tag = "3")]
    pub content_type: ::prost::alloc::string::String,
    #[prost(message, repeated, tag = "4")]
    pub content_fields: ::prost::alloc::vec::Vec<StoreContentFieldModel>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct StoreContentResponse {
    #[prost(bool, tag = "1")]
    pub status: bool,
    #[prost(message, optional, tag = "2")]
    pub data: ::core::option::Option<ContentModel>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct GetContentRequest {
    #[prost(string, tag = "1")]
    pub content_id: ::prost::alloc::string::String,
    #[prost(string, tag = "2")]
    pub content_type: ::prost::alloc::string::String,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct GetContentResponse {
    #[prost(bool, tag = "1")]
    pub status: bool,
    #[prost(message, optional, tag = "2")]
    pub data: ::core::option::Option<ContentModel>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UpdateContentFieldModel {
    #[prost(string, tag = "1")]
    pub name: ::prost::alloc::string::String,
    #[prost(string, tag = "2")]
    pub identifier: ::prost::alloc::string::String,
    #[prost(string, tag = "3")]
    pub data_type: ::prost::alloc::string::String,
    #[prost(string, tag = "4")]
    pub field_type: ::prost::alloc::string::String,
    #[prost(message, optional, tag = "5")]
    pub field_content: ::core::option::Option<ContentFieldFieldContent>,
    #[prost(message, optional, tag = "6")]
    pub field_data: ::core::option::Option<ContentFieldData>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UpdateContentRequest {
    #[prost(string, tag = "1")]
    pub content_id: ::prost::alloc::string::String,
    #[prost(string, tag = "2")]
    pub name: ::prost::alloc::string::String,
    #[prost(string, tag = "3")]
    pub content_type: ::prost::alloc::string::String,
    #[prost(message, repeated, tag = "4")]
    pub content_fields: ::prost::alloc::vec::Vec<UpdateContentFieldModel>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UpdateContentResponse {
    #[prost(bool, tag = "1")]
    pub status: bool,
    #[prost(message, optional, tag = "2")]
    pub data: ::core::option::Option<ContentModel>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct PutContentIdentifierRequest {
    #[prost(string, tag = "1")]
    pub content_id: ::prost::alloc::string::String,
    #[prost(string, tag = "2")]
    pub identifier: ::prost::alloc::string::String,
    #[prost(string, tag = "3")]
    pub content_type: ::prost::alloc::string::String,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct PutContentIdentifierResponse {
    #[prost(bool, tag = "1")]
    pub status: bool,
    #[prost(message, optional, tag = "2")]
    pub data: ::core::option::Option<ContentModel>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct DeleteContentRequest {
    #[prost(string, tag = "1")]
    pub content_id: ::prost::alloc::string::String,
    #[prost(string, tag = "2")]
    pub content_type: ::prost::alloc::string::String,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct DeleteContentResponse {
    #[prost(bool, tag = "1")]
    pub status: bool,
}
/// Generated client implementations.
pub mod content_client {
    #![allow(
        unused_variables,
        dead_code,
        missing_docs,
        clippy::wildcard_imports,
        clippy::let_unit_value
    )]
    use tonic::codegen::http::Uri;
    use tonic::codegen::*;
    #[derive(Debug, Clone)]
    pub struct ContentClient<T> {
        inner: tonic::client::Grpc<T>,
    }
    impl ContentClient<tonic::transport::Channel> {
        /// Attempt to create a new client by connecting to a given endpoint.
        pub async fn connect<D>(dst: D) -> Result<Self, tonic::transport::Error>
        where
            D: TryInto<tonic::transport::Endpoint>,
            D::Error: Into<StdError>,
        {
            let conn = tonic::transport::Endpoint::new(dst)?.connect().await?;
            Ok(Self::new(conn))
        }
    }
    impl<T> ContentClient<T>
    where
        T: tonic::client::GrpcService<tonic::body::Body>,
        T::Error: Into<StdError>,
        T::ResponseBody: Body<Data = Bytes> + std::marker::Send + 'static,
        <T::ResponseBody as Body>::Error: Into<StdError> + std::marker::Send,
    {
        pub fn new(inner: T) -> Self {
            let inner = tonic::client::Grpc::new(inner);
            Self { inner }
        }
        pub fn with_origin(inner: T, origin: Uri) -> Self {
            let inner = tonic::client::Grpc::with_origin(inner, origin);
            Self { inner }
        }
        pub fn with_interceptor<F>(
            inner: T,
            interceptor: F,
        ) -> ContentClient<InterceptedService<T, F>>
        where
            F: tonic::service::Interceptor,
            T::ResponseBody: Default,
            T: tonic::codegen::Service<
                http::Request<tonic::body::Body>,
                Response = http::Response<
                    <T as tonic::client::GrpcService<tonic::body::Body>>::ResponseBody,
                >,
            >,
            <T as tonic::codegen::Service<http::Request<tonic::body::Body>>>::Error:
                Into<StdError> + std::marker::Send + std::marker::Sync,
        {
            ContentClient::new(InterceptedService::new(inner, interceptor))
        }
        /// Compress requests with the given encoding.
        ///
        /// This requires the server to support it otherwise it might respond with an
        /// error.
        #[must_use]
        pub fn send_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.inner = self.inner.send_compressed(encoding);
            self
        }
        /// Enable decompressing responses.
        #[must_use]
        pub fn accept_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.inner = self.inner.accept_compressed(encoding);
            self
        }
        /// Limits the maximum size of a decoded message.
        ///
        /// Default: `4MB`
        #[must_use]
        pub fn max_decoding_message_size(mut self, limit: usize) -> Self {
            self.inner = self.inner.max_decoding_message_size(limit);
            self
        }
        /// Limits the maximum size of an encoded message.
        ///
        /// Default: `usize::MAX`
        #[must_use]
        pub fn max_encoding_message_size(mut self, limit: usize) -> Self {
            self.inner = self.inner.max_encoding_message_size(limit);
            self
        }
        pub async fn collection_all(
            &mut self,
            request: impl tonic::IntoRequest<super::CollectionAllRequest>,
        ) -> std::result::Result<tonic::Response<super::CollectionAllResponse>, tonic::Status>
        {
            self.inner.ready().await.map_err(|e| {
                tonic::Status::unknown(format!("Service was not ready: {}", e.into()))
            })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static("/content.content/CollectionAll");
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("content.content", "CollectionAll"));
            self.inner.unary(req, path, codec).await
        }
        pub async fn get_collection(
            &mut self,
            request: impl tonic::IntoRequest<super::GetCollectionRequest>,
        ) -> std::result::Result<tonic::Response<super::GetCollectionResponse>, tonic::Status>
        {
            self.inner.ready().await.map_err(|e| {
                tonic::Status::unknown(format!("Service was not ready: {}", e.into()))
            })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static("/content.content/GetCollection");
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("content.content", "GetCollection"));
            self.inner.unary(req, path, codec).await
        }
        pub async fn store_collection(
            &mut self,
            request: impl tonic::IntoRequest<super::StoreCollectionRequest>,
        ) -> std::result::Result<tonic::Response<super::StoreCollectionResponse>, tonic::Status>
        {
            self.inner.ready().await.map_err(|e| {
                tonic::Status::unknown(format!("Service was not ready: {}", e.into()))
            })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static("/content.content/StoreCollection");
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("content.content", "StoreCollection"));
            self.inner.unary(req, path, codec).await
        }
        pub async fn update_collection(
            &mut self,
            request: impl tonic::IntoRequest<super::UpdateCollectionRequest>,
        ) -> std::result::Result<tonic::Response<super::UpdateCollectionResponse>, tonic::Status>
        {
            self.inner.ready().await.map_err(|e| {
                tonic::Status::unknown(format!("Service was not ready: {}", e.into()))
            })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static("/content.content/UpdateCollection");
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("content.content", "UpdateCollection"));
            self.inner.unary(req, path, codec).await
        }
        pub async fn content_paginate(
            &mut self,
            request: impl tonic::IntoRequest<super::ContentPaginateRequest>,
        ) -> std::result::Result<tonic::Response<super::ContentPaginateResponse>, tonic::Status>
        {
            self.inner.ready().await.map_err(|e| {
                tonic::Status::unknown(format!("Service was not ready: {}", e.into()))
            })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static("/content.content/ContentPaginate");
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("content.content", "ContentPaginate"));
            self.inner.unary(req, path, codec).await
        }
        pub async fn store_content(
            &mut self,
            request: impl tonic::IntoRequest<super::StoreContentRequest>,
        ) -> std::result::Result<tonic::Response<super::StoreContentResponse>, tonic::Status>
        {
            self.inner.ready().await.map_err(|e| {
                tonic::Status::unknown(format!("Service was not ready: {}", e.into()))
            })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static("/content.content/StoreContent");
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("content.content", "StoreContent"));
            self.inner.unary(req, path, codec).await
        }
        pub async fn get_content(
            &mut self,
            request: impl tonic::IntoRequest<super::GetContentRequest>,
        ) -> std::result::Result<tonic::Response<super::GetContentResponse>, tonic::Status>
        {
            self.inner.ready().await.map_err(|e| {
                tonic::Status::unknown(format!("Service was not ready: {}", e.into()))
            })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static("/content.content/GetContent");
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("content.content", "GetContent"));
            self.inner.unary(req, path, codec).await
        }
        pub async fn update_content(
            &mut self,
            request: impl tonic::IntoRequest<super::UpdateContentRequest>,
        ) -> std::result::Result<tonic::Response<super::UpdateContentResponse>, tonic::Status>
        {
            self.inner.ready().await.map_err(|e| {
                tonic::Status::unknown(format!("Service was not ready: {}", e.into()))
            })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static("/content.content/UpdateContent");
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("content.content", "UpdateContent"));
            self.inner.unary(req, path, codec).await
        }
        pub async fn put_content_identifier(
            &mut self,
            request: impl tonic::IntoRequest<super::PutContentIdentifierRequest>,
        ) -> std::result::Result<tonic::Response<super::PutContentIdentifierResponse>, tonic::Status>
        {
            self.inner.ready().await.map_err(|e| {
                tonic::Status::unknown(format!("Service was not ready: {}", e.into()))
            })?;
            let codec = tonic::codec::ProstCodec::default();
            let path =
                http::uri::PathAndQuery::from_static("/content.content/PutContentIdentifier");
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("content.content", "PutContentIdentifier"));
            self.inner.unary(req, path, codec).await
        }
        pub async fn delete_content(
            &mut self,
            request: impl tonic::IntoRequest<super::DeleteContentRequest>,
        ) -> std::result::Result<tonic::Response<super::DeleteContentResponse>, tonic::Status>
        {
            self.inner.ready().await.map_err(|e| {
                tonic::Status::unknown(format!("Service was not ready: {}", e.into()))
            })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static("/content.content/DeleteContent");
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("content.content", "DeleteContent"));
            self.inner.unary(req, path, codec).await
        }
    }
}
/// Generated server implementations.
pub mod content_server {
    #![allow(
        unused_variables,
        dead_code,
        missing_docs,
        clippy::wildcard_imports,
        clippy::let_unit_value
    )]
    use tonic::codegen::*;
    /// Generated trait containing gRPC methods that should be implemented for use with ContentServer.
    #[async_trait]
    pub trait Content: std::marker::Send + std::marker::Sync + 'static {
        async fn collection_all(
            &self,
            request: tonic::Request<super::CollectionAllRequest>,
        ) -> std::result::Result<tonic::Response<super::CollectionAllResponse>, tonic::Status>;
        async fn get_collection(
            &self,
            request: tonic::Request<super::GetCollectionRequest>,
        ) -> std::result::Result<tonic::Response<super::GetCollectionResponse>, tonic::Status>;
        async fn store_collection(
            &self,
            request: tonic::Request<super::StoreCollectionRequest>,
        ) -> std::result::Result<tonic::Response<super::StoreCollectionResponse>, tonic::Status>;
        async fn update_collection(
            &self,
            request: tonic::Request<super::UpdateCollectionRequest>,
        ) -> std::result::Result<tonic::Response<super::UpdateCollectionResponse>, tonic::Status>;
        async fn content_paginate(
            &self,
            request: tonic::Request<super::ContentPaginateRequest>,
        ) -> std::result::Result<tonic::Response<super::ContentPaginateResponse>, tonic::Status>;
        async fn store_content(
            &self,
            request: tonic::Request<super::StoreContentRequest>,
        ) -> std::result::Result<tonic::Response<super::StoreContentResponse>, tonic::Status>;
        async fn get_content(
            &self,
            request: tonic::Request<super::GetContentRequest>,
        ) -> std::result::Result<tonic::Response<super::GetContentResponse>, tonic::Status>;
        async fn update_content(
            &self,
            request: tonic::Request<super::UpdateContentRequest>,
        ) -> std::result::Result<tonic::Response<super::UpdateContentResponse>, tonic::Status>;
        async fn put_content_identifier(
            &self,
            request: tonic::Request<super::PutContentIdentifierRequest>,
        ) -> std::result::Result<tonic::Response<super::PutContentIdentifierResponse>, tonic::Status>;
        async fn delete_content(
            &self,
            request: tonic::Request<super::DeleteContentRequest>,
        ) -> std::result::Result<tonic::Response<super::DeleteContentResponse>, tonic::Status>;
    }
    #[derive(Debug)]
    pub struct ContentServer<T> {
        inner: Arc<T>,
        accept_compression_encodings: EnabledCompressionEncodings,
        send_compression_encodings: EnabledCompressionEncodings,
        max_decoding_message_size: Option<usize>,
        max_encoding_message_size: Option<usize>,
    }
    impl<T> ContentServer<T> {
        pub fn new(inner: T) -> Self {
            Self::from_arc(Arc::new(inner))
        }
        pub fn from_arc(inner: Arc<T>) -> Self {
            Self {
                inner,
                accept_compression_encodings: Default::default(),
                send_compression_encodings: Default::default(),
                max_decoding_message_size: None,
                max_encoding_message_size: None,
            }
        }
        pub fn with_interceptor<F>(inner: T, interceptor: F) -> InterceptedService<Self, F>
        where
            F: tonic::service::Interceptor,
        {
            InterceptedService::new(Self::new(inner), interceptor)
        }
        /// Enable decompressing requests with the given encoding.
        #[must_use]
        pub fn accept_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.accept_compression_encodings.enable(encoding);
            self
        }
        /// Compress responses with the given encoding, if the client supports it.
        #[must_use]
        pub fn send_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.send_compression_encodings.enable(encoding);
            self
        }
        /// Limits the maximum size of a decoded message.
        ///
        /// Default: `4MB`
        #[must_use]
        pub fn max_decoding_message_size(mut self, limit: usize) -> Self {
            self.max_decoding_message_size = Some(limit);
            self
        }
        /// Limits the maximum size of an encoded message.
        ///
        /// Default: `usize::MAX`
        #[must_use]
        pub fn max_encoding_message_size(mut self, limit: usize) -> Self {
            self.max_encoding_message_size = Some(limit);
            self
        }
    }
    impl<T, B> tonic::codegen::Service<http::Request<B>> for ContentServer<T>
    where
        T: Content,
        B: Body + std::marker::Send + 'static,
        B::Error: Into<StdError> + std::marker::Send + 'static,
    {
        type Response = http::Response<tonic::body::Body>;
        type Error = std::convert::Infallible;
        type Future = BoxFuture<Self::Response, Self::Error>;
        fn poll_ready(
            &mut self,
            _cx: &mut Context<'_>,
        ) -> Poll<std::result::Result<(), Self::Error>> {
            Poll::Ready(Ok(()))
        }
        fn call(&mut self, req: http::Request<B>) -> Self::Future {
            match req.uri().path() {
                "/content.content/CollectionAll" => {
                    #[allow(non_camel_case_types)]
                    struct CollectionAllSvc<T: Content>(pub Arc<T>);
                    impl<T: Content> tonic::server::UnaryService<super::CollectionAllRequest> for CollectionAllSvc<T> {
                        type Response = super::CollectionAllResponse;
                        type Future = BoxFuture<tonic::Response<Self::Response>, tonic::Status>;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::CollectionAllRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as Content>::collection_all(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = CollectionAllSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/content.content/GetCollection" => {
                    #[allow(non_camel_case_types)]
                    struct GetCollectionSvc<T: Content>(pub Arc<T>);
                    impl<T: Content> tonic::server::UnaryService<super::GetCollectionRequest> for GetCollectionSvc<T> {
                        type Response = super::GetCollectionResponse;
                        type Future = BoxFuture<tonic::Response<Self::Response>, tonic::Status>;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::GetCollectionRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as Content>::get_collection(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = GetCollectionSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/content.content/StoreCollection" => {
                    #[allow(non_camel_case_types)]
                    struct StoreCollectionSvc<T: Content>(pub Arc<T>);
                    impl<T: Content> tonic::server::UnaryService<super::StoreCollectionRequest>
                        for StoreCollectionSvc<T>
                    {
                        type Response = super::StoreCollectionResponse;
                        type Future = BoxFuture<tonic::Response<Self::Response>, tonic::Status>;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::StoreCollectionRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as Content>::store_collection(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = StoreCollectionSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/content.content/UpdateCollection" => {
                    #[allow(non_camel_case_types)]
                    struct UpdateCollectionSvc<T: Content>(pub Arc<T>);
                    impl<T: Content> tonic::server::UnaryService<super::UpdateCollectionRequest>
                        for UpdateCollectionSvc<T>
                    {
                        type Response = super::UpdateCollectionResponse;
                        type Future = BoxFuture<tonic::Response<Self::Response>, tonic::Status>;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::UpdateCollectionRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as Content>::update_collection(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = UpdateCollectionSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/content.content/ContentPaginate" => {
                    #[allow(non_camel_case_types)]
                    struct ContentPaginateSvc<T: Content>(pub Arc<T>);
                    impl<T: Content> tonic::server::UnaryService<super::ContentPaginateRequest>
                        for ContentPaginateSvc<T>
                    {
                        type Response = super::ContentPaginateResponse;
                        type Future = BoxFuture<tonic::Response<Self::Response>, tonic::Status>;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::ContentPaginateRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as Content>::content_paginate(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = ContentPaginateSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/content.content/StoreContent" => {
                    #[allow(non_camel_case_types)]
                    struct StoreContentSvc<T: Content>(pub Arc<T>);
                    impl<T: Content> tonic::server::UnaryService<super::StoreContentRequest> for StoreContentSvc<T> {
                        type Response = super::StoreContentResponse;
                        type Future = BoxFuture<tonic::Response<Self::Response>, tonic::Status>;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::StoreContentRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut =
                                async move { <T as Content>::store_content(&inner, request).await };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = StoreContentSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/content.content/GetContent" => {
                    #[allow(non_camel_case_types)]
                    struct GetContentSvc<T: Content>(pub Arc<T>);
                    impl<T: Content> tonic::server::UnaryService<super::GetContentRequest> for GetContentSvc<T> {
                        type Response = super::GetContentResponse;
                        type Future = BoxFuture<tonic::Response<Self::Response>, tonic::Status>;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::GetContentRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut =
                                async move { <T as Content>::get_content(&inner, request).await };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = GetContentSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/content.content/UpdateContent" => {
                    #[allow(non_camel_case_types)]
                    struct UpdateContentSvc<T: Content>(pub Arc<T>);
                    impl<T: Content> tonic::server::UnaryService<super::UpdateContentRequest> for UpdateContentSvc<T> {
                        type Response = super::UpdateContentResponse;
                        type Future = BoxFuture<tonic::Response<Self::Response>, tonic::Status>;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::UpdateContentRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as Content>::update_content(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = UpdateContentSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/content.content/PutContentIdentifier" => {
                    #[allow(non_camel_case_types)]
                    struct PutContentIdentifierSvc<T: Content>(pub Arc<T>);
                    impl<T: Content> tonic::server::UnaryService<super::PutContentIdentifierRequest>
                        for PutContentIdentifierSvc<T>
                    {
                        type Response = super::PutContentIdentifierResponse;
                        type Future = BoxFuture<tonic::Response<Self::Response>, tonic::Status>;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::PutContentIdentifierRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as Content>::put_content_identifier(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = PutContentIdentifierSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/content.content/DeleteContent" => {
                    #[allow(non_camel_case_types)]
                    struct DeleteContentSvc<T: Content>(pub Arc<T>);
                    impl<T: Content> tonic::server::UnaryService<super::DeleteContentRequest> for DeleteContentSvc<T> {
                        type Response = super::DeleteContentResponse;
                        type Future = BoxFuture<tonic::Response<Self::Response>, tonic::Status>;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::DeleteContentRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as Content>::delete_content(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = DeleteContentSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                _ => Box::pin(async move {
                    let mut response = http::Response::new(tonic::body::Body::default());
                    let headers = response.headers_mut();
                    headers.insert(
                        tonic::Status::GRPC_STATUS,
                        (tonic::Code::Unimplemented as i32).into(),
                    );
                    headers.insert(
                        http::header::CONTENT_TYPE,
                        tonic::metadata::GRPC_CONTENT_TYPE,
                    );
                    Ok(response)
                }),
            }
        }
    }
    impl<T> Clone for ContentServer<T> {
        fn clone(&self) -> Self {
            let inner = self.inner.clone();
            Self {
                inner,
                accept_compression_encodings: self.accept_compression_encodings,
                send_compression_encodings: self.send_compression_encodings,
                max_decoding_message_size: self.max_decoding_message_size,
                max_encoding_message_size: self.max_encoding_message_size,
            }
        }
    }
    /// Generated gRPC service name
    pub const SERVICE_NAME: &str = "content.content";
    impl<T> tonic::server::NamedService for ContentServer<T> {
        const NAME: &'static str = SERVICE_NAME;
    }
}
