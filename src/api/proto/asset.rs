// This file is @generated by prost-build.
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct FolderTypeMetaData {
    #[prost(string, tag = "1")]
    pub color: ::prost::alloc::string::String,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct FileTypeMetaData {
    #[prost(string, tag = "1")]
    pub file_type: ::prost::alloc::string::String,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct MetaDataType {
    #[prost(message, optional, tag = "1")]
    pub file_meta_data: ::core::option::Option<FileTypeMetaData>,
    #[prost(message, optional, tag = "2")]
    pub folder_meta_data: ::core::option::Option<FolderTypeMetaData>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct AssetModel {
    #[prost(string, tag = "1")]
    pub id: ::prost::alloc::string::String,
    #[prost(string, optional, tag = "2")]
    pub parent_id: ::core::option::Option<::prost::alloc::string::String>,
    #[prost(string, tag = "3")]
    pub name: ::prost::alloc::string::String,
    #[prost(string, tag = "4")]
    pub new_path: ::prost::alloc::string::String,
    #[prost(string, tag = "5")]
    pub asset_type: ::prost::alloc::string::String,
    #[prost(message, optional, tag = "6")]
    pub metadata: ::core::option::Option<MetaDataType>,
    #[prost(message, optional, tag = "7")]
    pub created_at: ::core::option::Option<::prost_types::Timestamp>,
    #[prost(message, optional, tag = "8")]
    pub updated_at: ::core::option::Option<::prost_types::Timestamp>,
    #[prost(string, tag = "9")]
    pub created_by: ::prost::alloc::string::String,
    #[prost(string, tag = "10")]
    pub updated_by: ::prost::alloc::string::String,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct AssetPaginateRequest {
    #[prost(int64, optional, tag = "1")]
    pub page: ::core::option::Option<i64>,
    #[prost(string, optional, tag = "2")]
    pub order: ::core::option::Option<::prost::alloc::string::String>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct AssetPaginateResponse {
    #[prost(bool, tag = "1")]
    pub status: bool,
    #[prost(message, optional, tag = "2")]
    pub data: ::core::option::Option<asset_paginate_response::AssetPaginateData>,
}
/// Nested message and enum types in `AssetPaginateResponse`.
pub mod asset_paginate_response {
    #[derive(Clone, Copy, PartialEq, ::prost::Message)]
    pub struct AssetPagination {
        #[prost(int64, tag = "1")]
        pub total: i64,
    }
    #[derive(Clone, PartialEq, ::prost::Message)]
    pub struct AssetPaginateData {
        #[prost(message, optional, tag = "1")]
        pub pagination: ::core::option::Option<AssetPagination>,
        #[prost(message, repeated, tag = "2")]
        pub data: ::prost::alloc::vec::Vec<super::AssetModel>,
    }
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct CreateFolderRequest {
    #[prost(string, tag = "1")]
    pub name: ::prost::alloc::string::String,
    #[prost(string, optional, tag = "2")]
    pub parent_id: ::core::option::Option<::prost::alloc::string::String>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct CreateFolderResponse {
    #[prost(bool, tag = "1")]
    pub status: bool,
    #[prost(message, optional, tag = "2")]
    pub data: ::core::option::Option<AssetModel>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct DeleteAssetRequest {
    #[prost(string, tag = "1")]
    pub asset_id: ::prost::alloc::string::String,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct DeleteAssetResponse {
    #[prost(bool, tag = "1")]
    pub status: bool,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct DeleteFolderRequest {
    #[prost(string, tag = "1")]
    pub folder_id: ::prost::alloc::string::String,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct DeleteFolderResponse {
    #[prost(bool, tag = "1")]
    pub status: bool,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RenameAssetRequest {
    #[prost(string, tag = "1")]
    pub asset_id: ::prost::alloc::string::String,
    #[prost(string, tag = "2")]
    pub name: ::prost::alloc::string::String,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RenameAssetResponse {
    #[prost(bool, tag = "1")]
    pub status: bool,
    #[prost(message, optional, tag = "2")]
    pub data: ::core::option::Option<AssetModel>,
}
/// Generated client implementations.
pub mod asset_client {
    #![allow(
        unused_variables,
        dead_code,
        missing_docs,
        clippy::wildcard_imports,
        clippy::let_unit_value
    )]
    use tonic::codegen::http::Uri;
    use tonic::codegen::*;
    #[derive(Debug, Clone)]
    pub struct AssetClient<T> {
        inner: tonic::client::Grpc<T>,
    }
    impl AssetClient<tonic::transport::Channel> {
        /// Attempt to create a new client by connecting to a given endpoint.
        pub async fn connect<D>(dst: D) -> Result<Self, tonic::transport::Error>
        where
            D: TryInto<tonic::transport::Endpoint>,
            D::Error: Into<StdError>,
        {
            let conn = tonic::transport::Endpoint::new(dst)?.connect().await?;
            Ok(Self::new(conn))
        }
    }
    impl<T> AssetClient<T>
    where
        T: tonic::client::GrpcService<tonic::body::Body>,
        T::Error: Into<StdError>,
        T::ResponseBody: Body<Data = Bytes> + std::marker::Send + 'static,
        <T::ResponseBody as Body>::Error: Into<StdError> + std::marker::Send,
    {
        pub fn new(inner: T) -> Self {
            let inner = tonic::client::Grpc::new(inner);
            Self { inner }
        }
        pub fn with_origin(inner: T, origin: Uri) -> Self {
            let inner = tonic::client::Grpc::with_origin(inner, origin);
            Self { inner }
        }
        pub fn with_interceptor<F>(
            inner: T,
            interceptor: F,
        ) -> AssetClient<InterceptedService<T, F>>
        where
            F: tonic::service::Interceptor,
            T::ResponseBody: Default,
            T: tonic::codegen::Service<
                http::Request<tonic::body::Body>,
                Response = http::Response<
                    <T as tonic::client::GrpcService<tonic::body::Body>>::ResponseBody,
                >,
            >,
            <T as tonic::codegen::Service<http::Request<tonic::body::Body>>>::Error:
                Into<StdError> + std::marker::Send + std::marker::Sync,
        {
            AssetClient::new(InterceptedService::new(inner, interceptor))
        }
        /// Compress requests with the given encoding.
        ///
        /// This requires the server to support it otherwise it might respond with an
        /// error.
        #[must_use]
        pub fn send_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.inner = self.inner.send_compressed(encoding);
            self
        }
        /// Enable decompressing responses.
        #[must_use]
        pub fn accept_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.inner = self.inner.accept_compressed(encoding);
            self
        }
        /// Limits the maximum size of a decoded message.
        ///
        /// Default: `4MB`
        #[must_use]
        pub fn max_decoding_message_size(mut self, limit: usize) -> Self {
            self.inner = self.inner.max_decoding_message_size(limit);
            self
        }
        /// Limits the maximum size of an encoded message.
        ///
        /// Default: `usize::MAX`
        #[must_use]
        pub fn max_encoding_message_size(mut self, limit: usize) -> Self {
            self.inner = self.inner.max_encoding_message_size(limit);
            self
        }
        pub async fn paginate(
            &mut self,
            request: impl tonic::IntoRequest<super::AssetPaginateRequest>,
        ) -> std::result::Result<tonic::Response<super::AssetPaginateResponse>, tonic::Status>
        {
            self.inner.ready().await.map_err(|e| {
                tonic::Status::unknown(format!("Service was not ready: {}", e.into()))
            })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static("/asset.Asset/Paginate");
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("asset.Asset", "Paginate"));
            self.inner.unary(req, path, codec).await
        }
        pub async fn create_folder(
            &mut self,
            request: impl tonic::IntoRequest<super::CreateFolderRequest>,
        ) -> std::result::Result<tonic::Response<super::CreateFolderResponse>, tonic::Status>
        {
            self.inner.ready().await.map_err(|e| {
                tonic::Status::unknown(format!("Service was not ready: {}", e.into()))
            })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static("/asset.Asset/CreateFolder");
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("asset.Asset", "CreateFolder"));
            self.inner.unary(req, path, codec).await
        }
        pub async fn delete_asset(
            &mut self,
            request: impl tonic::IntoRequest<super::DeleteAssetRequest>,
        ) -> std::result::Result<tonic::Response<super::DeleteAssetResponse>, tonic::Status>
        {
            self.inner.ready().await.map_err(|e| {
                tonic::Status::unknown(format!("Service was not ready: {}", e.into()))
            })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static("/asset.Asset/DeleteAsset");
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("asset.Asset", "DeleteAsset"));
            self.inner.unary(req, path, codec).await
        }
        pub async fn delete_folder(
            &mut self,
            request: impl tonic::IntoRequest<super::DeleteFolderRequest>,
        ) -> std::result::Result<tonic::Response<super::DeleteFolderResponse>, tonic::Status>
        {
            self.inner.ready().await.map_err(|e| {
                tonic::Status::unknown(format!("Service was not ready: {}", e.into()))
            })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static("/asset.Asset/DeleteFolder");
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("asset.Asset", "DeleteFolder"));
            self.inner.unary(req, path, codec).await
        }
        pub async fn rename_asset(
            &mut self,
            request: impl tonic::IntoRequest<super::RenameAssetRequest>,
        ) -> std::result::Result<tonic::Response<super::RenameAssetResponse>, tonic::Status>
        {
            self.inner.ready().await.map_err(|e| {
                tonic::Status::unknown(format!("Service was not ready: {}", e.into()))
            })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static("/asset.Asset/RenameAsset");
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("asset.Asset", "RenameAsset"));
            self.inner.unary(req, path, codec).await
        }
    }
}
/// Generated server implementations.
pub mod asset_server {
    #![allow(
        unused_variables,
        dead_code,
        missing_docs,
        clippy::wildcard_imports,
        clippy::let_unit_value
    )]
    use tonic::codegen::*;
    /// Generated trait containing gRPC methods that should be implemented for use with AssetServer.
    #[async_trait]
    pub trait Asset: std::marker::Send + std::marker::Sync + 'static {
        async fn paginate(
            &self,
            request: tonic::Request<super::AssetPaginateRequest>,
        ) -> std::result::Result<tonic::Response<super::AssetPaginateResponse>, tonic::Status>;
        async fn create_folder(
            &self,
            request: tonic::Request<super::CreateFolderRequest>,
        ) -> std::result::Result<tonic::Response<super::CreateFolderResponse>, tonic::Status>;
        async fn delete_asset(
            &self,
            request: tonic::Request<super::DeleteAssetRequest>,
        ) -> std::result::Result<tonic::Response<super::DeleteAssetResponse>, tonic::Status>;
        async fn delete_folder(
            &self,
            request: tonic::Request<super::DeleteFolderRequest>,
        ) -> std::result::Result<tonic::Response<super::DeleteFolderResponse>, tonic::Status>;
        async fn rename_asset(
            &self,
            request: tonic::Request<super::RenameAssetRequest>,
        ) -> std::result::Result<tonic::Response<super::RenameAssetResponse>, tonic::Status>;
    }
    #[derive(Debug)]
    pub struct AssetServer<T> {
        inner: Arc<T>,
        accept_compression_encodings: EnabledCompressionEncodings,
        send_compression_encodings: EnabledCompressionEncodings,
        max_decoding_message_size: Option<usize>,
        max_encoding_message_size: Option<usize>,
    }
    impl<T> AssetServer<T> {
        pub fn new(inner: T) -> Self {
            Self::from_arc(Arc::new(inner))
        }
        pub fn from_arc(inner: Arc<T>) -> Self {
            Self {
                inner,
                accept_compression_encodings: Default::default(),
                send_compression_encodings: Default::default(),
                max_decoding_message_size: None,
                max_encoding_message_size: None,
            }
        }
        pub fn with_interceptor<F>(inner: T, interceptor: F) -> InterceptedService<Self, F>
        where
            F: tonic::service::Interceptor,
        {
            InterceptedService::new(Self::new(inner), interceptor)
        }
        /// Enable decompressing requests with the given encoding.
        #[must_use]
        pub fn accept_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.accept_compression_encodings.enable(encoding);
            self
        }
        /// Compress responses with the given encoding, if the client supports it.
        #[must_use]
        pub fn send_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.send_compression_encodings.enable(encoding);
            self
        }
        /// Limits the maximum size of a decoded message.
        ///
        /// Default: `4MB`
        #[must_use]
        pub fn max_decoding_message_size(mut self, limit: usize) -> Self {
            self.max_decoding_message_size = Some(limit);
            self
        }
        /// Limits the maximum size of an encoded message.
        ///
        /// Default: `usize::MAX`
        #[must_use]
        pub fn max_encoding_message_size(mut self, limit: usize) -> Self {
            self.max_encoding_message_size = Some(limit);
            self
        }
    }
    impl<T, B> tonic::codegen::Service<http::Request<B>> for AssetServer<T>
    where
        T: Asset,
        B: Body + std::marker::Send + 'static,
        B::Error: Into<StdError> + std::marker::Send + 'static,
    {
        type Response = http::Response<tonic::body::Body>;
        type Error = std::convert::Infallible;
        type Future = BoxFuture<Self::Response, Self::Error>;
        fn poll_ready(
            &mut self,
            _cx: &mut Context<'_>,
        ) -> Poll<std::result::Result<(), Self::Error>> {
            Poll::Ready(Ok(()))
        }
        fn call(&mut self, req: http::Request<B>) -> Self::Future {
            match req.uri().path() {
                "/asset.Asset/Paginate" => {
                    #[allow(non_camel_case_types)]
                    struct PaginateSvc<T: Asset>(pub Arc<T>);
                    impl<T: Asset> tonic::server::UnaryService<super::AssetPaginateRequest> for PaginateSvc<T> {
                        type Response = super::AssetPaginateResponse;
                        type Future = BoxFuture<tonic::Response<Self::Response>, tonic::Status>;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::AssetPaginateRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move { <T as Asset>::paginate(&inner, request).await };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = PaginateSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/asset.Asset/CreateFolder" => {
                    #[allow(non_camel_case_types)]
                    struct CreateFolderSvc<T: Asset>(pub Arc<T>);
                    impl<T: Asset> tonic::server::UnaryService<super::CreateFolderRequest> for CreateFolderSvc<T> {
                        type Response = super::CreateFolderResponse;
                        type Future = BoxFuture<tonic::Response<Self::Response>, tonic::Status>;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::CreateFolderRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut =
                                async move { <T as Asset>::create_folder(&inner, request).await };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = CreateFolderSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/asset.Asset/DeleteAsset" => {
                    #[allow(non_camel_case_types)]
                    struct DeleteAssetSvc<T: Asset>(pub Arc<T>);
                    impl<T: Asset> tonic::server::UnaryService<super::DeleteAssetRequest> for DeleteAssetSvc<T> {
                        type Response = super::DeleteAssetResponse;
                        type Future = BoxFuture<tonic::Response<Self::Response>, tonic::Status>;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::DeleteAssetRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut =
                                async move { <T as Asset>::delete_asset(&inner, request).await };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = DeleteAssetSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/asset.Asset/DeleteFolder" => {
                    #[allow(non_camel_case_types)]
                    struct DeleteFolderSvc<T: Asset>(pub Arc<T>);
                    impl<T: Asset> tonic::server::UnaryService<super::DeleteFolderRequest> for DeleteFolderSvc<T> {
                        type Response = super::DeleteFolderResponse;
                        type Future = BoxFuture<tonic::Response<Self::Response>, tonic::Status>;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::DeleteFolderRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut =
                                async move { <T as Asset>::delete_folder(&inner, request).await };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = DeleteFolderSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/asset.Asset/RenameAsset" => {
                    #[allow(non_camel_case_types)]
                    struct RenameAssetSvc<T: Asset>(pub Arc<T>);
                    impl<T: Asset> tonic::server::UnaryService<super::RenameAssetRequest> for RenameAssetSvc<T> {
                        type Response = super::RenameAssetResponse;
                        type Future = BoxFuture<tonic::Response<Self::Response>, tonic::Status>;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::RenameAssetRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut =
                                async move { <T as Asset>::rename_asset(&inner, request).await };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = RenameAssetSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                _ => Box::pin(async move {
                    let mut response = http::Response::new(tonic::body::Body::default());
                    let headers = response.headers_mut();
                    headers.insert(
                        tonic::Status::GRPC_STATUS,
                        (tonic::Code::Unimplemented as i32).into(),
                    );
                    headers.insert(
                        http::header::CONTENT_TYPE,
                        tonic::metadata::GRPC_CONTENT_TYPE,
                    );
                    Ok(response)
                }),
            }
        }
    }
    impl<T> Clone for AssetServer<T> {
        fn clone(&self) -> Self {
            let inner = self.inner.clone();
            Self {
                inner,
                accept_compression_encodings: self.accept_compression_encodings,
                send_compression_encodings: self.send_compression_encodings,
                max_decoding_message_size: self.max_decoding_message_size,
                max_encoding_message_size: self.max_encoding_message_size,
            }
        }
    }
    /// Generated gRPC service name
    pub const SERVICE_NAME: &str = "asset.Asset";
    impl<T> tonic::server::NamedService for AssetServer<T> {
        const NAME: &'static str = SERVICE_NAME;
    }
}
